import { useEffect } from "react";
import { Header } from "@/components/Header";
import { StudioCard } from "@/components/StudioCard";
import { FilterControls } from "@/components/FilterControls";
import { FilterModal } from "@/components/FilterModal";
import { MapView } from "@/components/MapView";
import { useSearchFilter } from "@/contexts/SearchFilterContext";
import { Salon } from "@/types";
import salon1 from "@/assets/salon-1.jpg";
import salon2 from "@/assets/salon-2.jpg";
import salon3 from "@/assets/salon-3.jpg";
import salon4 from "@/assets/salon-4.jpg";
import salon5 from "@/assets/salon-5.jpg";
import salon6 from "@/assets/salon-6.jpg";

// Convert the existing studios data to match Salon interface
const salonsData: Salon[] = [
  {
    id: "1",
    name: "Chic Hair Studio",
    description: "Modern hair styling studio with expert stylists and premium services.",
    location: "Downtown",
    address: "123 Market Street, San Francisco, CA 94102",
    distance: "10 miles away",
    rating: 4.8,
    reviews: 124,
    images: [salon1],
    ownerId: "owner-1",
    isActive: true,
    coordinates: { lat: 37.7749, lng: -122.4194 },
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Urban Beauty Lounge",
    description: "Full-service beauty lounge offering hair, nails, and skincare services.",
    location: "Mission District",
    address: "456 Mission Street, San Francisco, CA 94110",
    distance: "5 miles away",
    rating: 4.9,
    reviews: 89,
    images: [salon2],
    ownerId: "owner-2",
    isActive: true,
    coordinates: { lat: 37.7599, lng: -122.4148 },
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-02T00:00:00Z",
  },
  {
    id: "3",
    name: "The Style Loft",
    description: "Trendy salon specializing in cutting-edge styles and color techniques.",
    location: "Marina",
    address: "789 Marina Blvd, San Francisco, CA 94123",
    distance: "8 miles away",
    rating: 4.7,
    reviews: 156,
    images: [salon3],
    ownerId: "owner-3",
    isActive: true,
    coordinates: { lat: 37.8021, lng: -122.4364 },
    createdAt: "2024-01-03T00:00:00Z",
    updatedAt: "2024-01-03T00:00:00Z",
  },
  {
    id: "4",
    name: "Glamour Zone",
    description: "Luxury salon experience with personalized beauty treatments.",
    location: "SoMa",
    address: "321 Howard Street, San Francisco, CA 94105",
    distance: "3 miles away",
    rating: 4.6,
    reviews: 98,
    images: [salon4],
    ownerId: "owner-4",
    isActive: true,
    coordinates: { lat: 37.7749, lng: -122.4094 },
    createdAt: "2024-01-04T00:00:00Z",
    updatedAt: "2024-01-04T00:00:00Z",
  },
  {
    id: "5",
    name: "Hair Haven",
    description: "Cozy neighborhood salon with friendly staff and affordable prices.",
    location: "Richmond",
    address: "654 Geary Blvd, San Francisco, CA 94118",
    distance: "12 miles away",
    rating: 4.5,
    reviews: 67,
    images: [salon5],
    ownerId: "owner-5",
    isActive: true,
    coordinates: { lat: 37.7806, lng: -122.4644 },
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-05T00:00:00Z",
  },
  {
    id: "6",
    name: "The Beauty Spot",
    description: "Premium beauty destination with expert stylists and luxury amenities.",
    location: "North Beach",
    address: "987 Columbus Ave, San Francisco, CA 94133",
    distance: "7 miles away",
    rating: 4.9,
    reviews: 203,
    images: [salon6],
    ownerId: "owner-6",
    isActive: true,
    coordinates: { lat: 37.8067, lng: -122.4103 },
    createdAt: "2024-01-06T00:00:00Z",
    updatedAt: "2024-01-06T00:00:00Z",
  },
  {
    id: "7",
    name: "Style & Grace Salon",
    description: "Elegant salon offering classic and contemporary styling services.",
    location: "Sunset",
    address: "147 Irving Street, San Francisco, CA 94122",
    distance: "15 miles away",
    rating: 4.8,
    reviews: 134,
    images: [salon1],
    ownerId: "owner-7",
    isActive: true,
    coordinates: { lat: 37.7431, lng: -122.4697 },
    createdAt: "2024-01-07T00:00:00Z",
    updatedAt: "2024-01-07T00:00:00Z",
  },
  {
    id: "8",
    name: "The Hair Studio",
    description: "Professional hair studio in the heart of the Financial District.",
    location: "Financial District",
    address: "258 Montgomery Street, San Francisco, CA 94104",
    distance: "2 miles away",
    rating: 4.7,
    reviews: 178,
    images: [salon2],
    ownerId: "owner-8",
    isActive: true,
    coordinates: { lat: 37.7946, lng: -122.4014 },
    createdAt: "2024-01-08T00:00:00Z",
    updatedAt: "2024-01-08T00:00:00Z",
  },
];

const Index = () => {
  const {
    filteredSalons,
    setAllSalons,
    viewMode,
    filters
  } = useSearchFilter();

  // Initialize salon data
  useEffect(() => {
    setAllSalons(salonsData);
  }, [setAllSalons]);

  return (
    <div className="min-h-screen bg-white">
      <Header />

      <main className="max-w-7xl mx-auto px-6 lg:px-8 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">
            {filteredSalons.length > 0
              ? `${filteredSalons.length} place${filteredSalons.length !== 1 ? 's' : ''} in San Francisco`
              : 'No places found'
            }
          </h1>
          <FilterControls />
        </div>

        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredSalons.map((salon) => (
              <StudioCard
                key={salon.id}
                id={salon.id}
                name={salon.name}
                location={salon.location}
                distance={salon.distance}
                rating={salon.rating}
                price={Math.round(salon.rating * 20)} // Mock price calculation
                image={salon.images[0]}
              />
            ))}
          </div>
        ) : (
          <MapView />
        )}

        {filteredSalons.length === 0 && viewMode === 'grid' && (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-glamspot-neutral-900 mb-2">
              No salons match your criteria
            </h3>
            <p className="text-glamspot-neutral-600 mb-4">
              Try adjusting your search or filters to find more options.
            </p>
          </div>
        )}
      </main>

      <FilterModal />
    </div>
  );
};

export default Index;
