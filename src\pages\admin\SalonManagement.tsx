import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu';
import { 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye, 
  MapPin,
  Star,
  Building2
} from 'lucide-react';
import { Salon } from '@/types';

const SalonManagement = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSalon, setSelectedSalon] = useState<Salon | null>(null);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);

  // Mock data - in real app, this would come from API
  const salons: Salon[] = [
    {
      id: '1',
      name: 'The Hair Lounge',
      description: 'Premier salon in the heart of San Francisco',
      location: 'San Francisco',
      address: '123 Market Street, San Francisco, CA 94102',
      distance: 'Downtown',
      rating: 4.8,
      reviews: 120,
      images: ['/salon-1.jpg'],
      ownerId: 'owner-1',
      isActive: true,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    },
    {
      id: '2',
      name: 'Bella Beauty Studio',
      description: 'Full-service beauty salon and spa',
      location: 'San Francisco',
      address: '456 Union Square, San Francisco, CA 94108',
      distance: 'Union Square',
      rating: 4.6,
      reviews: 89,
      images: ['/salon-2.jpg'],
      ownerId: 'owner-2',
      isActive: true,
      createdAt: '2024-01-02T00:00:00Z',
      updatedAt: '2024-01-02T00:00:00Z',
    },
    {
      id: '3',
      name: 'Style & Grace',
      description: 'Modern hair styling and color specialists',
      location: 'San Francisco',
      address: '789 Castro Street, San Francisco, CA 94114',
      distance: 'Castro District',
      rating: 4.9,
      reviews: 156,
      images: ['/salon-3.jpg'],
      ownerId: 'owner-3',
      isActive: false,
      createdAt: '2024-01-03T00:00:00Z',
      updatedAt: '2024-01-03T00:00:00Z',
    },
  ];

  const filteredSalons = salons.filter(salon =>
    salon.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    salon.location.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleViewSalon = (salon: Salon) => {
    navigate(`/admin/salons/${salon.id}`);
  };

  const handleEditSalon = (salon: Salon) => {
    // TODO: Implement edit functionality
    console.log('Edit salon:', salon);
  };

  const handleDeleteSalon = (salon: Salon) => {
    // TODO: Implement delete functionality
    console.log('Delete salon:', salon);
  };

  const handleToggleStatus = (salon: Salon) => {
    // TODO: Implement status toggle
    console.log('Toggle status for salon:', salon);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-glamspot-neutral-900">Salon Management</h1>
          <p className="text-glamspot-neutral-600 mt-2">
            Manage all salon listings and their information
          </p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-glamspot-primary hover:bg-glamspot-primary-dark">
              <Plus className="w-4 h-4 mr-2" />
              Add Salon
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Salon</DialogTitle>
              <DialogDescription>
                Create a new salon listing in the system
              </DialogDescription>
            </DialogHeader>
            {/* TODO: Add salon form component */}
            <div className="p-4 text-center text-glamspot-neutral-500">
              Salon form component will be implemented here
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-glamspot-neutral-400 w-4 h-4" />
              <Input
                placeholder="Search salons by name or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Badge variant="outline" className="text-glamspot-neutral-600">
              {filteredSalons.length} salon{filteredSalons.length !== 1 ? 's' : ''}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Salons Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="w-5 h-5 text-glamspot-primary" />
            All Salons
          </CardTitle>
          <CardDescription>
            Complete list of all salon listings in the system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Salon</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Rating</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Created</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredSalons.map((salon) => (
                <TableRow key={salon.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-glamspot-neutral-200 rounded-lg flex items-center justify-center">
                        <Building2 className="w-5 h-5 text-glamspot-neutral-600" />
                      </div>
                      <div>
                        <p className="font-medium text-glamspot-neutral-900">{salon.name}</p>
                        <p className="text-sm text-glamspot-neutral-500 truncate max-w-xs">
                          {salon.description}
                        </p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-glamspot-neutral-600">
                      <MapPin className="w-4 h-4" />
                      <span className="text-sm">{salon.distance}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1">
                      <Star className="w-4 h-4 text-yellow-400 fill-current" />
                      <span className="font-medium">{salon.rating}</span>
                      <span className="text-sm text-glamspot-neutral-500">
                        ({salon.reviews})
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={salon.isActive ? 'default' : 'secondary'}
                      className={salon.isActive ? 'bg-green-100 text-green-800' : ''}
                    >
                      {salon.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-sm text-glamspot-neutral-500">
                    {new Date(salon.createdAt).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewSalon(salon)}>
                          <Eye className="w-4 h-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEditSalon(salon)}>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Salon
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleStatus(salon)}>
                          <Building2 className="w-4 h-4 mr-2" />
                          {salon.isActive ? 'Deactivate' : 'Activate'}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteSalon(salon)}
                          className="text-red-600"
                        >
                          <Trash2 className="w-4 h-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Salon Details Dialog */}
      <Dialog open={!!selectedSalon} onOpenChange={() => setSelectedSalon(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{selectedSalon?.name}</DialogTitle>
            <DialogDescription>Salon details and information</DialogDescription>
          </DialogHeader>
          {selectedSalon && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Location</label>
                  <p className="text-glamspot-neutral-900">{selectedSalon.location}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-glamspot-neutral-700">Rating</label>
                  <p className="text-glamspot-neutral-900">{selectedSalon.rating} ({selectedSalon.reviews} reviews)</p>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Address</label>
                <p className="text-glamspot-neutral-900">{selectedSalon.address}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Description</label>
                <p className="text-glamspot-neutral-900">{selectedSalon.description}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-glamspot-neutral-700">Status</label>
                <Badge 
                  variant={selectedSalon.isActive ? 'default' : 'secondary'}
                  className={selectedSalon.isActive ? 'bg-green-100 text-green-800 ml-2' : 'ml-2'}
                >
                  {selectedSalon.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SalonManagement;
